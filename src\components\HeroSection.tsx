import React from 'react'
import { HeroContent } from '@/types'

interface HeroSectionProps {
  heroContent: HeroContent
}

const HeroSection: React.FC<HeroSectionProps> = ({ heroContent }) => {
  const scrollToVideoSection = () => {
    const videoSection = document.querySelector(
      '[data-section="video-showcase"]'
    )
    if (videoSection) {
      videoSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  return (
    <section className="py-44 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-6xl md:text-7xl font-normal text-gray-900 mb-8 leading-tight animate-fade-in">
          {heroContent.title}
        </h1>
        <p className="text-xl text-gray-600 mb-12 leading-relaxed animate-fade-in max-w-3xl mx-auto">
          {heroContent.description}
        </p>

        {/* Dotted Circle Arrow Button */}
        <div className="flex justify-center animate-fade-in">
          <button
            onClick={scrollToVideoSection}
            className="animate-bounce"
            aria-label="Scroll to video section"
          >
            <img src="/icons/ARROW.png" alt="Arrow" className="w-14 h-14" />
          </button>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
